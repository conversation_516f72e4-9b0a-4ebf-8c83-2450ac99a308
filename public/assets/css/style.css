/* ===================================
   MODERN RESPONSIVE DESIGN SYSTEM
   =================================== */

/* Root Variables for Consistent Design */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;

    --border-radius: 0.5rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.75rem;

    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    --font-family-sans-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Base Styles */
body {
    font-family: var(--font-family-sans-serif);
    background-color: #f8f9fa;
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Main Content Layout */
.main-content {
    flex: 1;
    min-height: calc(100vh - 200px);
}

/* Enhanced Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: var(--border-radius-sm);
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Table Responsive Design */
.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    background: white;
    margin-bottom: 1.5rem;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
    white-space: nowrap;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Mobile Table Scrolling */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: var(--border-radius-sm);
        margin: 0 -15px 1.5rem -15px;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    .table-responsive .table {
        min-width: 600px; /* Force horizontal scroll */
    }

    .table thead th,
    .table tbody td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Avatar Styling */
.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: var(--shadow-sm);
}

@media (max-width: 576px) {
    .avatar {
        width: 35px;
        height: 35px;
    }
}

/* Status Colors */
.status-booked {
    background-color: var(--primary-color) !important;
    font-weight: 600;
}

.status-completed {
    background-color: var(--success-color) !important;
    font-weight: 600;
}

.status-cancelled {
    background-color: var(--danger-color) !important;
    font-weight: 600;
}

.status-in-progress {
    background-color: var(--warning-color) !important;
    font-weight: 600;
}

/* Page Headers */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
}

.page-header h1, .page-header h2 {
    margin-bottom: 0;
    font-weight: 700;
}

/* Search and Filter Section */
.search-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* Loading States */
#loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    margin: 2rem 0;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

#main-content {
    display: none;
}

/* Utility Classes */
@media (max-width: 576px) {
    .text-truncate-mobile {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
    }
}

/* Badge Enhancements */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Mobile-first responsive utilities */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .mobile-hide {
        display: none !important;
    }
}

@media (min-width: 769px) {
    .mobile-show {
        display: none !important;
    }
}

/* ===================================
   CALENDAR STYLING
   =================================== */

#calendar {
    padding: 1.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

/* FullCalendar Customizations */
.fc-theme-standard td,
.fc-theme-standard th,
.fc-theme-standard .fc-scrollgrid {
    border-color: #dee2e6;
}

.fc-daygrid-day-number,
.fc-event-title,
.fc-list-item-title,
.fc-list-item-time,
.fc-col-header-cell-cushion,
.fc-day-other .fc-daygrid-day-number {
    color: var(--dark-color);
}

.fc-button-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.fc-button-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}
.day-events{
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 0.875rem; /* Increased font size */
    font-weight: bold; /* Made text bold */
}
.event-details {
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    background: white;
    box-shadow: var(--shadow-sm);
}

.event-details .patient-name {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.event-details .graft-count {
    color: var(--secondary-color);
    font-style: italic;
    margin-bottom: 0.25rem;
}

.event-details .status {
    font-weight: 600;
}

/* ===================================
   DROPZONE STYLING
   =================================== */

.dropzone {
    border: 2px dashed #ced4da;
    border-radius: var(--border-radius);
    background-color: var(--light-color);
    text-align: center;
    padding: 2rem;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropzone:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.dropzone.dz-drag-hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.dropzone .dz-message {
    margin: 0;
    font-size: 1.125rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.dropzone .dz-message .note {
    font-size: 0.875rem;
    color: var(--secondary-color);
    display: block;
    margin-top: 0.5rem;
}

/* ===================================
   PHOTO GALLERY STYLING
   ================================== */

.photo-gallery {
    margin-top: 2rem;
}

.photo-gallery .card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
}

.photo-gallery .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.photo-gallery .card img {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-gallery .card:hover img {
    transform: scale(1.05);
}

.photo-gallery .card-body {
    padding: 1rem;
}

.photo-gallery .card-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.photo-gallery .card-text {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* Mobile Photo Gallery */
@media (max-width: 576px) {
    .photo-gallery .card img {
        height: 150px;
    }

    .photo-gallery .card-body {
        padding: 0.75rem;
    }
}

/* ===================================
   MODAL ENHANCEMENTS
   =================================== */

/* Full-screen modals on mobile */
@media (max-width: 575.98px) {
    .modal-fullscreen-sm-down {
        width: 100vw;
        max-width: none;
        height: 100%;
        margin: 0;
    }

    .modal-fullscreen-sm-down .modal-content {
        height: 100vh;
        border: 0;
        border-radius: 0;
    }

    .modal-fullscreen-sm-down .modal-body {
        overflow-y: auto;
    }
}

/* Modal improvements */
.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: var(--light-color);
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: var(--light-color);
}

.modal-title {
    font-weight: 600;
}

/* Dropzone in modals */
.modal .dropzone {
    min-height: 150px;
    margin-top: 1rem;
}

@media (max-width: 575.98px) {
    .modal .dropzone {
        min-height: 200px;
    }
}

/* ===================================
   RESPONSIVE IMPROVEMENTS
   =================================== */

/* Mobile Navigation Improvements */
@media (max-width: 991px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }
}

/* Tablet Optimizations */
@media (max-width: 992px) and (min-width: 769px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.625rem;
        font-size: 0.9rem;
    }
}

/* Small Mobile Optimizations */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .search-section {
        padding: 1rem;
    }

    .page-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .page-header h1 {
        font-size: 1.75rem;
    }

    .page-header h2 {
        font-size: 1.5rem;
    }
}

/* Fix for delete button click area */
.delete-surgery-btn i,
.delete-item-btn i,
.delete-patient-btn i,
.delete-item-btn span,
.delete-patient-btn span,
.delete-surgery-btn span {
    pointer-events: none;
}
